#!/usr/bin/env python3
"""
直接测试API的脚本
"""

import requests
import json

def test_rag_qa_api():
    """测试RAG QA API"""
    
    url = "http://localhost:8080/api/v1/rag-qa"
    
    # 请求数据
    data = {
        "query": "点胶设计规范",
        "user_id": "test_user",
        "model_id": "qwen3_32b",
        "msg_id": "YOU_UUID",
        "conversation_id": "YOU_UUID",
        "history": [
            {"query": "什么是FPC？", "content": "FPC是柔性印刷电路板..."},
            {"query": "FPC点胶规范", "content": "点胶规范是..."}
        ],
        "stream": True,
        "top_k": 60,
        "enable_thinking": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bear default_api_access_token"  # 使用默认token
    }
    
    print("=== 测试RAG QA API ===")
    print(f"URL: {url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ 请求成功")
            # 如果是流式响应，打印前几行
            content = response.text
            lines = content.split('\n')[:10]  # 只打印前10行
            for line in lines:
                if line.strip():
                    print(f"响应行: {line}")
        else:
            print(f"✗ 请求失败")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        print(f"异常类型: {type(e)}")

if __name__ == "__main__":
    test_rag_qa_api()
