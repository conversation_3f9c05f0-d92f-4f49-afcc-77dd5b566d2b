"""
模型配置文件
包含各个LLM模型的配置信息
"""
import sys
import os

def get_auth_token(model_name):
    env_var_name = f"AUTH_TOKEN_{model_name.replace('-', '_').replace('.', '_').upper()}"
    token = os.environ.get(env_var_name)
    if not token:
        print(f"警告: 环境变量 {env_var_name} 未设置，请设置对应的授权令牌")
        return f"Bearer DEFAULT_TOKEN_FOR_{model_name}"  # 
    return f"Bearer {token}"

def get_api_key(model_name):
    env_var_name = f"AUTH_TOKEN_{model_name.replace('-', '_').replace('.', '_').upper()}"
    token = os.environ.get(env_var_name)
    if not token:
        print(f"警告: 环境变量 {env_var_name} 未设置，请设置对应的授权令牌")
        return f"Bearer DEFAULT_TOKEN_FOR_{model_name}"  # 
    return f"{token}"

def get_api_access_token():
    token = os.environ.get("API_ACCESS_TOKEN")
    if not token:
        print("警告: 环境变量 API_ACCESS_TOKEN 未设置，使用默认令牌")
        return "default_api_access_token"
    return f"Bear {token}"

MODEL_CONFIG = {
    "gpt_4o": {
        "service_type": "chatflow",
        "description": "GPT-4O Chatflow服务",
        "url": "https://mify-be.pt.xiaomi.com/api/v1/chat-messages",
        "max_tokens": 24000,  # 根据实际情况调整128K
        "timeout": 600,
        "headers": {
            "Authorization": get_auth_token("gpt_4o"),
            "Content-Type": "application/json"
        },
        "features": ["conversation", "intent_recognition"]
    },
    "qwen3_8b": {
        "service_type": "model",
        "description": "Qwen3-8B模型服务",
        "base_url": "http://**********:8001/v1",
        "api_key": get_api_key("qwen3_8b"),
        "model": "Qwen3-8B",
        "max_tokens": 4800,
        "temperature": 0.7,
        "top_p": 0.95,
        "timeout": 600,
        "features": ["intent_recognition", "openai_compatible"]
    },
    "qwen3_32b": {
        "service_type": "model",
        "description": "Qwen3-32B模型服务",
        "base_url": "http://*************:8000/v1",
        "api_key": get_api_key("qwen3_32b"),
        "model": "Qwen3-32B",
        "max_tokens": 4800,
        "temperature": 0.4,
        "top_p": 0.95,
        "timeout": 600,
        "features": [ "conversation", "openai_compatible"]
    }
}

def get_service_info(model_id: str) -> dict:
    """获取服务信息
    
    Args:
        model_id: 模型ID
        
    Returns:
        dict: 包含服务类型、描述和特性的信息
    """
    if model_id not in MODEL_CONFIG:
        raise ValueError(f"不支持的模型ID: {model_id}")
    
    config = MODEL_CONFIG[model_id]
    return {
        "model_id": model_id,
        "service_type": config.get("service_type", "unknown"),
        "description": config.get("description", ""),
        "features": config.get("features", [])
    }

def get_chatflow_services() -> list:
    """获取所有chatflow服务的模型ID列表"""
    return [model_id for model_id, config in MODEL_CONFIG.items() 
            if config.get("service_type") == "chatflow"]

def get_model_services() -> list:
    """获取所有model服务的模型ID列表"""
    return [model_id for model_id, config in MODEL_CONFIG.items() 
            if config.get("service_type") == "model"]
