#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DATAQA和RAGQA使用不同的rerank prompt
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompts.rag_qa_prompt import rag_qa_rerank_prompt
from prompts.data_qa_prompt import data_qa_rerank_prompt

def test_rerank_prompts():
    """测试两个rerank prompt是否不同"""
    print("=== RAGQA Rerank Prompt ===")
    print(rag_qa_rerank_prompt)
    print("\n=== DATAQA Rerank Prompt ===")
    print(data_qa_rerank_prompt)
    
    # 验证两个prompt不同
    assert rag_qa_rerank_prompt != data_qa_rerank_prompt, "RAGQA和DATAQA的rerank prompt应该不同"
    print("\n✅ 验证通过：RAGQA和DATAQA使用不同的rerank prompt")

if __name__ == "__main__":
    test_rerank_prompts() 