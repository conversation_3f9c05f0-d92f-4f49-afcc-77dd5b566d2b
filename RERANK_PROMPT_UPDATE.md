# Rerank Prompt 差异化配置

## 修改概述

根据需求，DATAQA和RAGQA两个类现在使用不同的rerank prompt，以更好地适应各自的业务场景。

## 修改内容

### 1. 新增DATAQA专用Rerank Prompt

在 `prompts/data_qa_prompt.py` 中新增了 `data_qa_rerank_prompt`：

```python
data_qa_rerank_prompt = """
根据用户查询，检索相关段落：
1. 涉及到的项目编码（如P81项目）完全一致的，且能回答用户查询的，排在首位，并且得分应该更高。
2. 涉及到的里程碑名称（如投板、挂板、发板等）与查询内容相关的，排在第二位，并且得分应该更高。
3. 包含时间节点信息的，排在第三位，并且得分应该更高。
4. 其他内容排在第四位，并且得分应该更低。
"""
```

### 2. 扩展RerankService功能

在 `services/rerank_service.py` 中新增了 `rerank_with_prompt` 方法：

```python
async def rerank_with_prompt(self, query, documents, instruction, top_r=None, min_score=None):
    """使用自定义prompt进行重排"""
    # ... 实现代码
```

同时保持了原有的 `rerank` 方法的向后兼容性：

```python
async def rerank(self, query, documents, top_r=None, min_score=None):
    """使用默认的rerank prompt进行重排"""
    return await self.rerank_with_prompt(query, documents, rerank_prompt, top_r, min_score)
```

### 3. 更新DATAQA类

在 `pipelines/data_qa.py` 中：

- 导入了 `data_qa_rerank_prompt`
- 修改了 `_retrieve_knowledge` 方法，使用专门的rerank prompt：

```python
reranked_docs = await self.rerank_service.rerank_with_prompt(
    query=query,
    documents=search_results,
    instruction=data_qa_rerank_prompt,
    top_r=top_r
)
```

## Prompt差异对比

### RAGQA Rerank Prompt（通用）
- 适用于PCB领域知识检索
- 重点关注项目编号、代码一致性
- 强调数字和英文代号的完全匹配

### DATAQA Rerank Prompt（专用）
- 针对项目管理和时间节点查询优化
- 重点关注项目编码匹配
- 强调里程碑名称和时间节点信息
- 更适合项目管理场景

## 配置说明

- **RAGQA**: 继续使用默认的 `rerank_prompt`（来自 `prompts/rag_qa_prompt.py`）
- **DATAQA**: 使用专门的 `data_qa_rerank_prompt`（来自 `prompts/data_qa_prompt.py`）

## 测试验证

通过测试验证了：
1. 两个prompt内容不同
2. DATAQA prompt包含项目管理相关关键词
3. RAGQA prompt包含PCB领域相关关键词
4. 向后兼容性得到保证

## 影响范围

- ✅ 不影响现有RAGQA功能
- ✅ 不影响现有API接口
- ✅ 保持向后兼容性
- ✅ 提升DATAQA在项目管理场景下的检索准确性 