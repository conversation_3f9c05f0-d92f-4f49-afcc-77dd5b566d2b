#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试top_r参数支持的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.schemas import RAGQARequest, DATAQARequest
from pipelines.rag_qa import RAGQA
from pipelines.data_qa import DATAQA

def test_schemas():
    """测试schemas中的top_r参数"""
    print("=== 测试schemas中的top_r参数 ===")
    
    # 测试RAGQARequest
    rag_request = RAGQARequest(
        query="测试查询",
        user_id="test_user",
        model_id="gpt_4o",
        msg_id="test_msg_id",
        conversation_id="test_conv_id",
        history=[],
        top_k=10,
        top_r=5
    )
    print(f"RAGQARequest top_r: {rag_request.top_r}")
    
    # 测试DATAQARequest
    data_request = DATAQARequest(
        query="测试查询",
        user_id="test_user",
        model_id="gpt_4o",
        msg_id="test_msg_id",
        conversation_id="test_conv_id",
        history=[],
        top_k=15,
        top_r=8
    )
    print(f"DATAQARequest top_r: {data_request.top_r}")
    print("✓ schemas测试通过\n")

def test_pipeline_methods():
    """测试pipeline方法中的top_r参数"""
    print("=== 测试pipeline方法中的top_r参数 ===")
    
    # 测试RAGQA的_retrieve_knowledge方法签名
    rag_qa = RAGQA("gpt_4o", "test_request_id")
    import inspect
    sig = inspect.signature(rag_qa._retrieve_knowledge)
    print(f"RAGQA._retrieve_knowledge参数: {list(sig.parameters.keys())}")
    
    # 测试DATAQA的_retrieve_knowledge方法签名
    data_qa = DATAQA("gpt_4o", "test_request_id")
    sig = inspect.signature(data_qa._retrieve_knowledge)
    print(f"DATAQA._retrieve_knowledge参数: {list(sig.parameters.keys())}")
    
    # 测试generate_stream方法签名
    sig = inspect.signature(rag_qa.generate_stream)
    print(f"RAGQA.generate_stream参数: {list(sig.parameters.keys())}")
    
    sig = inspect.signature(data_qa.generate_stream)
    print(f"DATAQA.generate_stream参数: {list(sig.parameters.keys())}")
    
    print("✓ pipeline方法测试通过\n")

if __name__ == "__main__":
    test_schemas()
    test_pipeline_methods()
    print("所有测试完成！top_r参数支持已正确实现。") 