"""API请求和响应模型定义"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"

# 错误响应模型
class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    error_code: str
    error_detail: Dict[str, Any] = {}

# 对话历史记录项
class HistoryItem(BaseModel):
    """对话历史记录项"""
    query: str
    content: str

# LLM问答请求
class LLMQARequest(BaseModel):
    """LLM问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    enable_thinking: bool = True

# LLM问答响应
class LLMQAResponse(BaseResponse):
    """LLM问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None

# 参考文档
class Reference(BaseModel):
    """参考文档模型"""
    title: str
    content: str
    score: float

# RAG问答请求
class RAGQARequest(BaseModel):
    """RAG问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True
    mode: Optional[str] = "strict"  # 模式：strict(严格模式) 或 common(普通模式)，默认为普通模式
    temperature: Optional[float] = None  # 温度参数，控制生成的随机性
    top_p: Optional[float] = None  # top_p参数，控制生成的多样性

# RAG问答响应
class RAGQAResponse(BaseResponse):
    """RAG问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None

# DATAQA问答请求
class DATAQARequest(BaseModel):
    """DATAQA问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True

# DATAQA问答响应
class DATAQAResponse(BaseResponse):
    """DATAQA问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None