#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试min_score参数支持的脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.schemas import RAGQARequest, DATAQARequest
from pipelines.rag_qa import RAGQA
from pipelines.data_qa import DATAQA

def test_schemas():
    """测试schemas中的min_score参数"""
    print("测试schemas中的min_score参数...")
    
    # 测试RAGQARequest
    rag_request = RAGQARequest(
        query="测试查询",
        user_id="test_user",
        model_id="gpt_4o",
        msg_id="test_msg_id",
        conversation_id="test_conv_id",
        history=[],
        min_score=0.7
    )
    print(f"RAGQARequest min_score: {rag_request.min_score}")
    assert rag_request.min_score == 0.7
    
    # 测试DATAQARequest
    data_request = DATAQARequest(
        query="测试查询",
        user_id="test_user",
        model_id="gpt_4o",
        msg_id="test_msg_id",
        conversation_id="test_conv_id",
        history=[],
        min_score=0.8
    )
    print(f"DATAQARequest min_score: {data_request.min_score}")
    assert data_request.min_score == 0.8
    
    print("✅ schemas测试通过")

def test_pipeline_methods():
    """测试pipeline方法中的min_score参数"""
    print("测试pipeline方法中的min_score参数...")
    
    # 测试RAGQA
    rag_qa = RAGQA(model_id="gpt_4o")
    
    # 检查方法签名
    import inspect
    rag_generate_sig = inspect.signature(rag_qa.generate)
    rag_stream_sig = inspect.signature(rag_qa.generate_stream)
    
    print(f"RAGQA.generate参数: {list(rag_generate_sig.parameters.keys())}")
    print(f"RAGQA.generate_stream参数: {list(rag_stream_sig.parameters.keys())}")
    
    assert 'min_score' in rag_generate_sig.parameters
    assert 'min_score' in rag_stream_sig.parameters
    
    # 测试DATAQA
    data_qa = DATAQA(model_id="gpt_4o")
    
    data_generate_sig = inspect.signature(data_qa.generate)
    data_stream_sig = inspect.signature(data_qa.generate_stream)
    
    print(f"DATAQA.generate参数: {list(data_generate_sig.parameters.keys())}")
    print(f"DATAQA.generate_stream参数: {list(data_stream_sig.parameters.keys())}")
    
    assert 'min_score' in data_generate_sig.parameters
    assert 'min_score' in data_stream_sig.parameters
    
    print("✅ pipeline方法测试通过")

if __name__ == "__main__":
    test_schemas()
    test_pipeline_methods()
    print("\n🎉 所有测试通过！min_score参数支持已成功添加。") 