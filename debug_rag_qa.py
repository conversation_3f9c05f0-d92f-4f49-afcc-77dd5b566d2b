#!/usr/bin/env python3
"""
调试RAG QA API问题的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志目录为当前目录，避免权限问题
os.environ["log_dir"] = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")

from core.schemas import RAGQARequest
import asyncio
import json

async def test_rag_qa_issue():
    """测试RAG QA的问题"""
    
    # 模拟请求数据
    request_data = {
        "query": "点胶设计规范",
        "user_id": "test_user",
        "model_id": "qwen3_32b",
        "msg_id": "YOU_UUID",
        "conversation_id": "YOU_UUID",
        "history": [
            {"query": "什么是FPC？", "content": "FPC是柔性印刷电路板..."},
            {"query": "FPC点胶规范", "content": "点胶规范是..."}
        ],
        "stream": True,
        "top_k": 60,
        "enable_thinking": True
    }
    
    print("=== 测试RAG QA问题 ===")
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
    
    try:
        # 创建请求对象
        request = RAGQARequest(**request_data)
        print(f"✓ RAGQARequest 创建成功")
        print(f"query: {request.query}")
        print(f"history: {request.history}")
        print(f"history type: {type(request.history)}")
        
        # 检查history中每个item的结构
        for i, item in enumerate(request.history):
            print(f"history[{i}]: {item}, type: {type(item)}")
            if isinstance(item, dict):
                print(f"  keys: {list(item.keys())}")
                print(f"  has 'query': {'query' in item}")
                print(f"  has 'content': {'content' in item}")
        
        # 测试实际的RAGQA实例创建和调用
        print("\n=== 测试 RAGQA 实例创建 ===")
        try:
            from pipelines.rag_qa import RAGQA
            qa = RAGQA(model_id=request.model_id, request_id=request.msg_id)
            print(f"✓ RAGQA 实例创建成功")

            # 测试_build_messages方法
            print("\n=== 测试 _build_messages 方法 ===")
            try:
                messages = qa._build_messages(request.query, request.history, "测试知识")
                print(f"✓ _build_messages 成功")
                print(f"messages 数量: {len(messages)}")
            except Exception as e:
                print(f"✗ _build_messages 失败: {e}")
                print(f"错误类型: {type(e)}")
                import traceback
                traceback.print_exc()

        except Exception as e:
            print(f"✗ RAGQA 实例创建失败: {e}")
            print(f"错误类型: {type(e)}")
            import traceback
            traceback.print_exc()

        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_rag_qa_issue())
