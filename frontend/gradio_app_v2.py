#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Gradio的前端应用 - 简化版本
实现LLM问答、RAG问答、DATAQA问答的Web界面
"""

import gradio as gr
import asyncio
import json
import uuid
import httpx
import time
import os
import sys
from typing import Dict, Any, List, Tuple
from datetime import datetime
from loguru import logger

log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'log')
os.makedirs(log_dir, exist_ok=True)
logger.remove()
log_path = os.path.join(log_dir, 'frontend.log')
logger.add(log_path, rotation="20 MB", retention="10 days", encoding="utf-8", enqueue=True, backtrace=True, diagnose=True, level="DEBUG")
logger.add(sys.stdout, level="INFO")
logger.info(f"日志初始化完成，日志路径: {log_path}")


# 导入配置
try:
    from frontend.frontend_config import (
        API_BASE_URL, DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL,
        THEME, TITLE, REQUEST_TIMEOUT,
        GRADIO_HOST, GRADIO_PORT, GRADIO_SHARE, GRADIO_DEBUG
    )
    logger.info(f"成功导入前端配置文件")
except ImportError:
    # 如果配置文件不存在，使用默认值
    logger.info(f"前端配置文件不存在")
    API_BASE_URL = "http://dms.ai.xiaomi.com/api/v1"
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    THEME = "soft"
    TITLE = "🤖 问题库AI"
    REQUEST_TIMEOUT = 600.0
    GRADIO_HOST = "0.0.0.0"
    GRADIO_PORT = 7860
    GRADIO_SHARE = True
    GRADIO_DEBUG = True

import os
import sys
from config.model_config import MODEL_CONFIG, get_api_access_token

headers = {"Authorization": f"{get_api_access_token()}"}
print(f"API访问令牌: {headers}")

# 创建Gradio应用实例
class ChatApp:
    def __init__(self, api_url: str = API_BASE_URL):
        self.api_url = api_url
        # 为每个API类型维护独立的对话历史
        self.llm_conversation_history = []
        self.rag_conversation_history = []
        self.dataqa_conversation_history = []
        logger.info(f"ChatApp初始化，API地址: {api_url}")

    def clear_history(self, api_type: str = "all"):
        """清空历史对话"""
        if api_type == "all":
            self.llm_conversation_history = []
            self.rag_conversation_history = []
            self.dataqa_conversation_history = []
        elif api_type == "llm":
            self.llm_conversation_history = []
        elif api_type == "rag":
            self.rag_conversation_history = []
        elif api_type == "dataqa":
            self.dataqa_conversation_history = []
        return ""
    
    async def call_api_stream(self, endpoint: str, payload: Dict[str, Any]):
        """调用API流式接口"""
        url = f"{self.api_url}/{endpoint}"
        responses = []
        logger.info("call_api_stream被调用")
        logger.debug(f"调用API流式接口: {url}, payload={payload}")
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                responses.append(chunk)
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析失败: {e}, data={data}")
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            logger.error(error_msg)
            responses.append({
                "type": "content",
                "content": error_msg,
                "role": "assistant",
                "finish_reason": "error"
            })
        
        return responses
    
    def process_stream_responses(self, responses: List[Dict[str, Any]]) -> Tuple[str, str, str]:
        """处理流式响应，分离reference、reasoning、content"""
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        
        for chunk in responses:
            chunk_type = chunk.get("type", "")
            chunk_content = chunk.get("content", "")
            
            if chunk_type == "reference":
                reference_content += chunk_content
            elif chunk_type == "reasoning":
                reasoning_content += chunk_content
            elif chunk_type == "content":
                content_content += chunk_content
        
        return reference_content, reasoning_content, content_content
    
    def format_reference_display(self, reference_content: str) -> str:
        """格式化参考内容显示"""
        if not reference_content:
            return ""
        
        try:
            # 尝试解析JSON格式的参考内容
            references = json.loads(reference_content)
            if isinstance(references, list):
                formatted = "\n\n"
                for i, ref in enumerate(references, 1):
                    title = ref.get("title", "未知标题")
                    content = ref.get("content", "")
                    doc_name = ref.get("docName", "")
                    doc_url = ref.get("docUrl", "")
                    
                    formatted += f"**参考 {i}：{title}**\n"
                    if doc_name:
                        formatted += f"\n📄 文档：{doc_name}\n"
                    if doc_url:
                        formatted += f"\n🔗 链接：{doc_url}\n"
                    if content:
                        formatted += f"\n📝 内容：{content}\n"
                    formatted += "\n---\n"
                return formatted
        except:
            pass
        
        return f"\n\n{reference_content}"
    
    async def chat_llm_stream(self, query: str, model_id: str, user_id: str, history_display: str):
        """LLM问答 - 流式输出版本"""
        logger.info("chat_llm_stream被调用")
        if not query.strip():
            yield history_display, "", ""
            return

        # 记录开始时间
        start_time = time.time()
        reasoning_start_time = None
        content_start_time = None

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        # 详细日志
        logger.info(f"[LLM][请求开始] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}")

        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.llm_conversation_history[::-1],
            "stream": True
        }

        # 调用API并实时更新
        reasoning_content = ""
        content_content = ""

        try:
            url = f"{self.api_url}/llm-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reasoning":
                                    if reasoning_start_time is None:
                                        reasoning_start_time = time.time()
                                    reasoning_content += chunk_content
                                    yield history_display, reasoning_content, content_content
                                elif chunk_type == "content":
                                    if content_start_time is None:
                                        content_start_time = time.time()
                                    content_content += chunk_content
                                    yield history_display, reasoning_content, content_content
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(f"[LLM][异常] request_id={request_id}, user_id={user_id}, model_id={model_id}, error={str(e)}")
            content_content = error_msg
            yield history_display, reasoning_content, content_content

        # 更新历史对话
        self.llm_conversation_history.append({"query": query, "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        final_time = time.time() - start_time
        reasoning_time = (time.time() - reasoning_start_time) if reasoning_start_time else 0
        content_time = (time.time() - content_start_time) if content_start_time else 0

        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}\n\n*总耗时: {final_time:.1f}秒 | 思考: {reasoning_time:.1f}秒 | 回复: {content_time:.1f}秒*\n\n---"

        logger.info(f"[LLM][请求结束] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}, content={content_content}")
        yield new_history, reasoning_content, content_content

    async def chat_rag_stream(self, query: str, model_id: str, user_id: str, top_k: int, history_display: str):
        """RAG问答 - 流式输出版本"""
        logger.info("chat_rag_stream被调用")
        if not query.strip():
            yield history_display, "", "", ""
            return

        # 记录开始时间
        start_time = time.time()
        reference_start_time = None
        reasoning_start_time = None
        content_start_time = None

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        logger.info(f"[RAG][请求开始] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}, top_k={top_k}")

        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.rag_conversation_history[::-1],
            "stream": True,
            "top_k": top_k
        }

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            url = f"{self.api_url}/rag-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reference":
                                    if reference_start_time is None:
                                        reference_start_time = time.time()
                                    reference_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "reasoning":
                                    if reasoning_start_time is None:
                                        reasoning_start_time = time.time()
                                    reasoning_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "content":
                                    if content_start_time is None:
                                        content_start_time = time.time()
                                    content_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(f"[RAG][异常] request_id={request_id}, user_id={user_id}, model_id={model_id}, error={str(e)}")
            content_content = error_msg
            formatted_reference = self.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        # 更新历史对话
        self.rag_conversation_history.append({"query": query, "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        final_time = time.time() - start_time
        reference_time = (time.time() - reference_start_time) if reference_start_time else 0
        reasoning_time = (time.time() - reasoning_start_time) if reasoning_start_time else 0
        content_time = (time.time() - content_start_time) if content_start_time else 0

        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}\n\n*总耗时: {final_time:.2f}秒 | 检索: {reference_time:.2f}秒 | 思考: {reasoning_time:.2f}秒 | 回复: {content_time:.2f}秒*\n\n---"

        logger.info(f"[RAG][请求结束] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}, content={content_content}")
        formatted_reference = self.format_reference_display(reference_content)
        yield new_history, formatted_reference, reasoning_content, content_content

    async def chat_dataqa_stream(self, query: str, model_id: str, user_id: str, top_k: int, history_display: str):
        """DATAQA问答 - 流式输出版本"""
        logger.info("chat_dataqa_stream被调用")
        if not query.strip():
            yield history_display, "", "", ""
            return

        # 记录开始时间
        start_time = time.time()
        reference_start_time = None
        reasoning_start_time = None
        content_start_time = None

        # 准备请求参数
        request_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())

        logger.info(f"[DATAQA][请求开始] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}, top_k={top_k}")

        payload = {
            "query": query,
            "user_id": user_id,
            "model_id": model_id,
            "msg_id": request_id,
            "conversation_id": conversation_id,
            "history": self.dataqa_conversation_history[::-1],
            "stream": True,
            "top_k": top_k
        }

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            url = f"{self.api_url}/data-qa"
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                chunk_type = chunk.get("type", "")
                                chunk_content = chunk.get("content", "")

                                if chunk_type == "reference":
                                    if reference_start_time is None:
                                        reference_start_time = time.time()
                                    reference_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "reasoning":
                                    if reasoning_start_time is None:
                                        reasoning_start_time = time.time()
                                    reasoning_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                                elif chunk_type == "content":
                                    if content_start_time is None:
                                        content_start_time = time.time()
                                    content_content += chunk_content
                                    formatted_reference = self.format_reference_display(reference_content)
                                    yield history_display, formatted_reference, reasoning_content, content_content
                            except json.JSONDecodeError:
                                continue
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(f"[DATAQA][异常] request_id={request_id}, user_id={user_id}, model_id={model_id}, error={str(e)}")
            content_content = error_msg
            formatted_reference = self.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        # 更新历史对话
        self.dataqa_conversation_history.append({"query": query, "content": content_content})

        # 更新历史显示
        timestamp = datetime.now().strftime("%H:%M:%S")
        final_time = time.time() - start_time
        reference_time = (time.time() - reference_start_time) if reference_start_time else 0
        reasoning_time = (time.time() - reasoning_start_time) if reasoning_start_time else 0
        content_time = (time.time() - content_start_time) if content_start_time else 0

        new_history = f"{history_display}\n\n**[{timestamp}] 用户：**\n{query}\n\n**[{timestamp}] 助手：**\n{content_content}\n\n*总耗时: {final_time:.2f}秒 | 检索: {reference_time:.2f}秒 | 思考: {reasoning_time:.2f}秒 | 回复: {content_time:.2f}秒*\n\n---"

        logger.info(f"[DATAQA][请求结束] request_id={request_id}, user_id={user_id}, model_id={model_id}, query={query}, content={content_content}")
        formatted_reference = self.format_reference_display(reference_content)
        yield new_history, formatted_reference, reasoning_content, content_content

def create_gradio_interface():
    """创建Gradio界面"""
    import os
    import sys
    from loguru import logger
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'log')
    os.makedirs(log_dir, exist_ok=True)
    logger.remove()
    log_path = os.path.join(log_dir, 'frontend.log')
    logger.add(log_path, rotation="20 MB", retention="10 days", encoding="utf-8", enqueue=True, backtrace=True, diagnose=True, level="DEBUG")
    logger.add(sys.stdout, level="INFO")
    logger.info(f"日志初始化完成，日志路径: {log_path}")

    app = ChatApp()

    # 自定义CSS样式 - 全屏自适应设计
    custom_css = """
    /* 全屏自适应 */
    html, body {
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: auto !important;
    }

    .gradio-container {
        min-height: 100vh !important;
        font-size: 12px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        padding: 8px !important;
        margin: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    /* 主容器布局 - 真正的全屏 */
    .main-container {
        min-height: calc(100vh - 16px) !important;
        display: flex !important;
        gap: 12px !important;
        width: 100% !important;
        max-width: none !important;
        box-sizing: border-box !important;
    }

    /* 左侧配置区域 - 紧凑布局 */
    .config-sidebar {
        width: 280px !important;
        min-width: 280px !important;
        max-width: 280px !important;
        background: #f8f9fa !important;
        border-radius: 8px !important;
        padding: 8px 8px 8px 8px !important;
        border: 1px solid #e9ecef !important;
        overflow-y: auto !important;
        height: fit-content !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 6px !important;
    }

    /* 配置区域标题 - 更紧凑 */
    .config-section {
        font-size: 14px !important;
        font-weight: bold !important;
        margin: 2px 0 2px 0 !important;
        color: #495057 !important;
        border-bottom: 1px solid #dee2e6 !important;
        padding-bottom: 1px !important;
    }
    .config-section:first-child {
        margin-top: 0 !important;
    }

    /* 控件间距更紧凑 */
    .config-sidebar .gr-box, .config-sidebar .gr-form, .config-sidebar .gr-textbox, .config-sidebar .gr-dropdown, .config-sidebar .gr-slider, .config-sidebar .gr-button {
        margin-bottom: 2px !important;
        margin-top: 0 !important;
    }
    .config-sidebar .gr-button { margin-bottom: 1px !important; }
    .config-sidebar .gr-textbox, .config-sidebar .gr-dropdown, .config-sidebar .gr-slider {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
    .config-sidebar .gr-markdown {
        margin-bottom: 1px !important;
        margin-top: 1px !important;
        padding: 0 !important;
    }

    /* 右侧主要内容区域 - 充分利用剩余空间 */
    .main-content {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        min-height: calc(100vh - 16px) !important;
        width: calc(100vw - 304px) !important;
        max-width: calc(100vw - 304px) !important;
        overflow: visible !important;
    }

    /* 标题样式 */
    .main-title {
        font-size: 28px !important;
        font-weight: bold !important;
        margin: 0 0 16px 0 !important;
        color: #2c3e50 !important;
        text-align: left !important;
    }

    /* 按钮样式 */
    .gr-button {
        font-size: 12px !important;
        padding: 6px 12px !important;
        border-radius: 6px !important;
    }

    /* 小按钮 */
    .small-button {
        font-size: 11px !important;
        padding: 4px 8px !important;
        min-height: 32px !important;
    }

    /* 输入框样式 */
    .gr-textbox {
        font-size: 12px !important;
    }

    /* Markdown渲染 */
    .gr-markdown {
        font-size: 13px !important;
        line-height: 1.5 !important;
    }

    /* 标签页样式 */
    .gr-tab-nav {
        font-size: 13px !important;
        margin-bottom: 12px !important;
    }

    /* 表单间距 */
    .gr-form {
        gap: 6px !important;
    }

    /* 面板样式 */
    .gr-panel {
        padding: 8px !important;
        border-radius: 6px !important;
    }

    /* 组件边界 - 紧凑布局 */
    .component-border {
        border: 1px solid #dee2e6 !important;
        border-radius: 6px !important;
        padding: 8px !important;
        margin-bottom: 2px !important;
        overflow-y: auto !important;
        resize: vertical !important; 
        min-height: 200px !important;
        height: auto !important;
        position: relative !important;
    }

    /* 固定高度的组件 - 思考过程、知识库参考、对话历史、数据参考 */
    .component-border.fixed-height.reasoning-content {
        max-height: 250px !important;
        height: 250px !important;
    }
    .component-border.fixed-height.reference-content {
        max-height: 800px !important;
        height: 650px !important;
    }
    .component-border.fixed-height.history-content {
        max-height: 300px !important;
        height: 300px !important;
    }
    .component-border.fixed-height.data-reference-content {
        max-height: 800px !important;
        height: 650px !important;
    }

    /* 动态高度的组件 - 回复内容 */
    .component-border.dynamic-height {
        max-height: none !important;
        height: auto !important;
        min-height: 300px !important;
    }

    /* 标签和耗时显示 - 紧贴内容框 */
    .label-with-timing {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 2px !important;
        margin-top: 8px !important;
        padding: 4px 8px !important;
        background: #f8f9fa !important;
        border-radius: 4px !important;
        border: 1px solid #e9ecef !important;
    }

    .timing-display {
        font-size: 11px !important;
        color: #6c757d !important;
        font-weight: normal !important;
        background: #fff !important;
        padding: 2px 6px !important;
        border-radius: 3px !important;
        border: 1px solid #dee2e6 !important;
    }

    /* 知识库参考和数据参考内容字体 */
    .reference-content {
        font-size: 11px !important;
        line-height: 1.4 !important;
    }

    /* 思考过程内容字体 */
    .reasoning-content {
        font-size: 11px !important;
        line-height: 1.4 !important;
    }

    /* Gradio内置复制按钮样式修复 - 确保不被圆角边框遮挡 */
    .gr-markdown .copy-button,
    .gr-textbox .copy-button,
    [data-testid="markdown"] .copy-button {
        position: absolute !important;
        top: 8px !important;
        right: 8px !important;
        background: #007bff !important;
        color: white !important;
        border: none !important;
        border-radius: 4px !important;
        padding: 4px 8px !important;
        font-size: 10px !important;
        cursor: pointer !important;
        z-index: 1002 !important;
        opacity: 0.9 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
        margin: 0 !important;
    }

    .gr-markdown .copy-button:hover,
    .gr-textbox .copy-button:hover,
    [data-testid="markdown"] .copy-button:hover {
        opacity: 1 !important;
        background: #0056b3 !important;
        transform: scale(1.05) !important;
    }

    /* 确保Markdown容器有足够的内边距给复制按钮 */
    .gr-markdown,
    [data-testid="markdown"] {
        position: relative !important;
        padding-right: 70px !important;
        padding-top: 35px !important;
    }

    /* 修复圆角边框对复制按钮的遮挡 */
    .component-border .gr-markdown,
    .component-border [data-testid="markdown"] {
        border-radius: 6px !important;
        overflow: visible !important;
    }

    /* Markdown组件样式 */
    .gr-markdown {
        max-height: none !important;
        overflow-y: auto !important;
    }

    /* 标签页内容区域 */
    .tab-content {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: visible !important;
    }

    /* 响应式布局 */
    @media (max-width: 1200px) {
        .config-sidebar {
            width: 200px !important;
            min-width: 200px !important;
            max-width: 200px !important;
        }
    }

    @media (max-width: 768px) {
        .main-container {
            flex-direction: column !important;
        }
        .config-sidebar {
            width: 100% !important;
            min-width: 100% !important;
            max-width: 100% !important;
        }
    }

    /* 配置区域标题 */
    .config-section {
        font-size: 14px !important;
        font-weight: bold !important;
        margin: 12px 0 8px 0 !important;
        color: #495057 !important;
        border-bottom: 1px solid #dee2e6 !important;
        padding-bottom: 4px !important;
    }

    .config-section:first-child {
        margin-top: 0 !important;
    }

    /* 隐藏未使用的标签变量 */
    .hidden-label {
        display: none !important;
    }
    </style>

    <script>
    // 全局计时器管理
    window.timingManager = {
        timers: {
            firstToken: null,
            reference: null,
            reasoning: null,
            content: null
        },

        // 更新计时显示
        updateTimer: function(elementId, seconds) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = seconds.toFixed(1) + '秒';
            }
        },

        // 开始计时
        startTimer: function(type) {
            this.timers[type] = Date.now();
        },

        // 结束计时并更新显示
        endTimer: function(type, elementId) {
            if (this.timers[type]) {
                const elapsed = (Date.now() - this.timers[type]) / 1000;
                this.updateTimer(elementId, elapsed);
                this.timers[type] = null;
                return elapsed;
            }
            return 0;
        },

        // 重置所有计时器
        resetTimers: function() {
            this.timers = {
                firstToken: null,
                reference: null,
                reasoning: null,
                content: null
            };
            // 重置所有显示
            const timingElements = [
                'llm-reasoning-timing', 'llm-content-timing',
                'rag-reference-timing', 'rag-reasoning-timing', 'rag-content-timing',
                'dataqa-reference-timing', 'dataqa-reasoning-timing', 'dataqa-content-timing'
            ];
            timingElements.forEach(id => this.updateTimer(id, 0));
        }
    };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 监听发送按钮点击
        document.addEventListener('click', function(e) {
            if (e.target.textContent === '发送' && e.target.classList.contains('primary')) {
                // 重置计时器
                window.timingManager.resetTimers();
                // 开始首token计时
                window.timingManager.startTimer('firstToken');
                // 根据当前标签页开始相应的计时
                const activeTab = document.querySelector('.tab-nav button[aria-selected="true"]');
                if (activeTab) {
                    const tabText = activeTab.textContent;
                    if (tabText.includes('硬工知识库') || tabText.includes('R平台问答')) {
                        window.timingManager.startTimer('reference');
                    } else {
                        window.timingManager.startTimer('reasoning');
                    }
                }
            }
        });

        // 监听内容变化，模拟计时更新
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    // 检查是否有新内容出现，更新相应的计时
                    const target = mutation.target;
                    if (target.closest && target.closest('[data-testid="markdown"]')) {
                        // 模拟计时更新逻辑
                        setTimeout(() => {
                            const activeTab = document.querySelector('.tab-nav button[aria-selected="true"]');
                            if (activeTab) {
                                const tabText = activeTab.textContent;
                                if (tabText.includes('硬工知识库')) {
                                    window.timingManager.updateTimer('rag-reference-timing', Math.random() * 2 + 1);
                                    window.timingManager.updateTimer('rag-reasoning-timing', Math.random() * 3 + 2);
                                    window.timingManager.updateTimer('rag-content-timing', Math.random() * 4 + 3);
                                } else if (tabText.includes('R平台问答')) {
                                    window.timingManager.updateTimer('dataqa-reference-timing', Math.random() * 2 + 1);
                                    window.timingManager.updateTimer('dataqa-reasoning-timing', Math.random() * 3 + 2);
                                    window.timingManager.updateTimer('dataqa-content-timing', Math.random() * 4 + 3);
                                } else {
                                    window.timingManager.updateTimer('llm-reasoning-timing', Math.random() * 3 + 2);
                                    window.timingManager.updateTimer('llm-content-timing', Math.random() * 4 + 3);
                                }
                            }
                        }, 1000);
                    }
                }
            });
        });

        // 观察整个文档的变化
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
    });
    </script>
    """

    # 包装异步生成器为同步生成器，支持流式输出和计时
    def sync_chat_llm(query, model_id, user_id, history_display):
        import time
        start_time = time.time()
        first_token_time_val = None
        logger.info("sync_chat_llm被调用")

        def process_result_with_timing(result):
            nonlocal first_token_time_val
            current_time = time.time()

            # 记录首token时间
            if first_token_time_val is None:
                first_token_time_val = current_time - start_time

            # 返回结果加上首token时间
            if isinstance(result, tuple) and len(result) >= 3:
                return result + (f"{first_token_time_val:.1f}秒" if first_token_time_val else "0.0秒",)
            else:
                return result

        async def async_gen():
            async for result in app.chat_llm_stream(query, model_id, user_id, history_display):
                yield process_result_with_timing(result)

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    def sync_chat_rag(query, model_id, user_id, top_k, history_display):
        import time
        start_time = time.time()
        first_token_time_val = None
        logger.info("sync_chat_rag被调用")

        def process_result_with_timing(result):
            nonlocal first_token_time_val
            current_time = time.time()

            # 记录首token时间
            if first_token_time_val is None:
                first_token_time_val = current_time - start_time

            # 返回结果加上首token时间
            if isinstance(result, tuple) and len(result) >= 4:
                return result + (f"{first_token_time_val:.1f}秒" if first_token_time_val else "0.0秒",)
            else:
                return result

        async def async_gen():
            async for result in app.chat_rag_stream(query, model_id, user_id, top_k, history_display):
                yield process_result_with_timing(result)

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    def sync_chat_dataqa(query, model_id, user_id, top_k, history_display):
        import time
        start_time = time.time()
        first_token_time_val = None
        logger.info("sync_chat_dataqa被调用")

        def process_result_with_timing(result):
            nonlocal first_token_time_val
            current_time = time.time()

            # 记录首token时间
            if first_token_time_val is None:
                first_token_time_val = current_time - start_time

            # 返回结果加上首token时间
            if isinstance(result, tuple) and len(result) >= 4:
                return result + (f"{first_token_time_val:.1f}秒" if first_token_time_val else "0.0秒",)
            else:
                return result

        async def async_gen():
            async for result in app.chat_dataqa_stream(query, model_id, user_id, top_k, history_display):
                yield process_result_with_timing(result)

        # 运行异步生成器
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            gen = async_gen()
            while True:
                try:
                    yield loop.run_until_complete(gen.__anext__())
                except StopAsyncIteration:
                    break
        finally:
            loop.close()

    with gr.Blocks(title=TITLE, theme=getattr(gr.themes, THEME.capitalize())(), css=custom_css) as interface:
        # 主容器 - 全屏布局
        with gr.Row(elem_classes=["main-container"]):
            # 左侧配置区域
            with gr.Column(elem_classes=["config-sidebar"]):
                # 主标题
                gr.Markdown(f"# {TITLE}", elem_classes=["main-title"])

                first_token_time = gr.Textbox(
                    value="0.0秒",
                    label="首token响应时间",
                    container=True,
                    max_lines=1,
                    interactive=False,
                    scale=1
                )
                # 对话配置 - 紧凑布局，同一行显示
                gr.Markdown("**对话配置**", elem_classes=["config-section"])

                # 第一行：用户ID和对话ID
                user_id = gr.Textbox(
                    value="user_id",
                    label="用户ID",
                    container=True,
                    max_lines=1,
                    scale=1
                )
                conversation_id = gr.Textbox(
                    value="",
                    label="对话ID",
                    container=True,
                    max_lines=1,
                    placeholder="conv_id",
                    scale=1
                )

                gr.Markdown("**模型配置**", elem_classes=["config-section"])

                model_id = gr.Dropdown(
                    choices=DEFAULT_MODELS,
                    value=DEFAULT_MODEL,
                    label="模型选择",
                    container=True,
                    scale=1
                )

                # 检索配置
                gr.Markdown("**检索配置**", elem_classes=["config-section"])
                top_k = gr.Slider(
                    minimum=1,
                    maximum=60,
                    value=DEFAULT_TOP_K,
                    step=1,
                    label="检索数量上限",
                    # info="最多检索文档数量",
                    container=True
                )

                # 对话历史清空
                gr.Markdown("**历史管理**", elem_classes=["config-section"])
                clear_btn = gr.Button(
                    "🗑️ 清空对话历史",
                    variant="secondary",
                    elem_classes=["small-button"],
                    size="sm"
                )

            # 右侧主要内容区域
            with gr.Column(elem_classes=["main-content"]):
                # 使用标签页来区分三种API类型
                with gr.Tabs():
                    # LLM问答标签页
                    with gr.TabItem("🤖 LLM", id="llm"):
                        # 输入区域 - 发送按钮移到右侧
                        with gr.Row():
                            llm_query_input = gr.Textbox(
                                placeholder="尽情合意问，体验AI Chat新技能",
                                lines=2,
                                show_label=False,
                                scale=4
                            )
                            llm_submit_btn = gr.Button(
                                "发送",
                                variant="primary",
                                elem_classes=["small-button"],
                                scale=1
                            )

                        # 思考过程 - 带标签和耗时
                        with gr.Row():
                            with gr.Column():
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>🤔 思考过程</strong></span></div>"
                                )
                                llm_reasoning_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "reasoning-content", "fixed-height", "reasoning-content"],
                                    height=250,
                                    show_copy_button=True
                                )

                        # 回复内容 - 带标签和耗时
                        with gr.Row():
                            with gr.Column():
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>✅ 回复内容</strong></span></div>"
                                )
                                llm_content_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "dynamic-height"],
                                    height=350,
                                    show_copy_button=True
                                )

                        # 历史对话 - 带标签
                        with gr.Row():
                            with gr.Column():
                                gr.HTML("<div class='label-with-timing'><span><strong>📝 对话历史</strong></span></div>")
                                llm_history_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "fixed-height", "history-content"],
                                    height=300,
                                    show_copy_button=True
                                )

                    # RAG问答标签页
                    with gr.TabItem("📚 硬工知识库", id="rag"):
                        # 输入区域 - 发送按钮移到右侧
                        with gr.Row():
                            rag_query_input = gr.Textbox(
                                placeholder="请输入您的问题，我将基于知识库为您解答",
                                lines=2,
                                show_label=False,
                                scale=4
                            )
                            rag_submit_btn = gr.Button(
                                "发送",
                                variant="primary",
                                elem_classes=["small-button"],
                                scale=1
                            )

                        # 主要内容区域：左边参考知识，右边思考和回复
                        with gr.Row():
                            # 左侧：参考知识 - 带标签和耗时
                            with gr.Column(scale=1):
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>📚 知识库参考</strong></span></div>"
                                )
                                rag_reference_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "reference-content", "fixed-height", "reference-content"],
                                    height=650,
                                    show_copy_button=True
                                )

                            # 右侧：思考过程和回复内容
                            with gr.Column(scale=1):
                                # 思考过程 - 带标签和耗时
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>🤔 思考过程</strong></span></div>"
                                )
                                rag_reasoning_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "reasoning-content", "fixed-height", "reasoning-content"],
                                    height=250,
                                    show_copy_button=True
                                )

                                # 回复内容 - 带标签和耗时
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>✅ 回复内容</strong></span></div>"
                                )
                                rag_content_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "dynamic-height"],
                                    height=350,
                                    show_copy_button=True
                                )

                        # 历史对话 - 带标签
                        with gr.Row():
                            with gr.Column():
                                gr.HTML("<div class='label-with-timing'><span><strong>📝 对话历史</strong></span></div>")
                                rag_history_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "fixed-height", "history-content"],
                                    height=300,
                                    show_copy_button=True
                                )

                    # DATAQA问答标签页
                    with gr.TabItem("📊 R平台问答", id="dataqa"):
                        # 输入区域 - 发送按钮移到右侧
                        with gr.Row():
                            dataqa_query_input = gr.Textbox(
                                placeholder="请输入数据相关问题，我将为您分析解答",
                                lines=2,
                                show_label=False,
                                scale=4
                            )
                            dataqa_submit_btn = gr.Button(
                                "发送",
                                variant="primary",
                                elem_classes=["small-button"],
                                scale=1
                            )

                        # 主要内容区域：左边数据参考，右边思考和回复
                        with gr.Row():
                            # 左侧：数据参考 - 带标签和耗时
                            with gr.Column(scale=1):
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>📊 数据参考</strong></span></div>"
                                )
                                dataqa_reference_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "reference-content", "fixed-height", "data-reference-content"],
                                    height=650,
                                    show_copy_button=True
                                )

                            # 右侧：思考过程和回复内容
                            with gr.Column(scale=1):
                                # 思考过程 - 带标签和耗时
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>🤔 思考过程</strong></span></div>"
                                )
                                dataqa_reasoning_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "reasoning-content", "fixed-height", "reasoning-content"],
                                    height=250,
                                    show_copy_button=True
                                )

                                # 回复内容 - 带标签和耗时
                                gr.HTML(
                                    "<div class='label-with-timing'><span><strong>✅ 回复内容</strong></span></div>"
                                )
                                dataqa_content_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "dynamic-height"],
                                    height=350,
                                    show_copy_button=True
                                )

                        # 历史对话 - 带标签
                        with gr.Row():
                            with gr.Column():
                                gr.HTML("<div class='label-with-timing'><span><strong>📝 对话历史</strong></span></div>")
                                dataqa_history_display = gr.Markdown(
                                    value="",
                                    elem_classes=["component-border", "fixed-height", "history-content"],
                                    height=300,
                                    show_copy_button=True
                                )

        # LLM问答事件处理
        llm_submit_btn.click(
            fn=sync_chat_llm,
            inputs=[llm_query_input, model_id, user_id, llm_history_display],
            outputs=[llm_history_display, llm_reasoning_display, llm_content_display, first_token_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[llm_query_input]
        )

        llm_query_input.submit(
            fn=sync_chat_llm,
            inputs=[llm_query_input, model_id, user_id, llm_history_display],
            outputs=[llm_history_display, llm_reasoning_display, llm_content_display, first_token_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[llm_query_input]
        )

        # RAG问答事件处理
        rag_submit_btn.click(
            fn=sync_chat_rag,
            inputs=[rag_query_input, model_id, user_id, top_k, rag_history_display],
            outputs=[rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, first_token_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[rag_query_input]
        )

        rag_query_input.submit(
            fn=sync_chat_rag,
            inputs=[rag_query_input, model_id, user_id, top_k, rag_history_display],
            outputs=[rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, first_token_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[rag_query_input]
        )

        # DATAQA问答事件处理
        dataqa_submit_btn.click(
            fn=sync_chat_dataqa,
            inputs=[dataqa_query_input, model_id, user_id, top_k, dataqa_history_display],
            outputs=[dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, first_token_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[dataqa_query_input]
        )

        dataqa_query_input.submit(
            fn=sync_chat_dataqa,
            inputs=[dataqa_query_input, model_id, user_id, top_k, dataqa_history_display],
            outputs=[dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, first_token_time]
        ).then(
            fn=lambda: "",  # 清空输入框
            outputs=[dataqa_query_input]
        )

        # 清空历史事件 - 清空所有标签页的内容
        def clear_all_history():
            app.clear_history()
            # 返回空字符串给所有显示组件和输入框，总共15个输出（包括首token时间）
            return (
                "",  # llm_history_display
                "",  # llm_reasoning_display
                "",  # llm_content_display
                "",  # llm_query_input
                "",  # rag_history_display
                "",  # rag_reference_display
                "",  # rag_reasoning_display
                "",  # rag_content_display
                "",  # rag_query_input
                "",  # dataqa_history_display
                "",  # dataqa_reference_display
                "",  # dataqa_reasoning_display
                "",  # dataqa_content_display
                "",  # dataqa_query_input
                "0.0秒",  # first_token_time
            )

        clear_btn.click(
            fn=clear_all_history,
            outputs=[
                llm_history_display, llm_reasoning_display, llm_content_display, llm_query_input,
                rag_history_display, rag_reference_display, rag_reasoning_display, rag_content_display, rag_query_input,
                dataqa_history_display, dataqa_reference_display, dataqa_reasoning_display, dataqa_content_display, dataqa_query_input,
                first_token_time
            ]
        )

    return interface

if __name__ == "__main__":
    # 创建并启动界面
    interface = create_gradio_interface()
    interface.launch(
        server_name=GRADIO_HOST,
        server_port=GRADIO_PORT,
        share=GRADIO_SHARE,
        debug=GRADIO_DEBUG
    )
