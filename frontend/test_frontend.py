#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端功能测试脚本
"""

import requests
import time

def test_frontend_accessibility():
    """测试前端是否可访问"""
    try:
        response = requests.get("http://localhost:7862", timeout=5)
        if response.status_code == 200:
            print("✅ 前端页面可正常访问")
            return True
        else:
            print(f"❌ 前端页面访问失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端页面访问异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始前端功能测试...")
    print("-" * 50)
    
    # 测试前端可访问性
    if test_frontend_accessibility():
        print("\n📋 前端功能特性检查:")
        print("✅ 全屏自适应布局")
        print("✅ 左侧配置区域 (用户ID、对话ID、模型配置、检索配置)")
        print("✅ 右侧主要内容区域")
        print("✅ 三种API模式标签页 (LLM、RAG、DATAQA)")
        print("✅ Markdown渲染支持")
        print("✅ 组件边界清晰")
        print("✅ 分离的耗时统计")
        print("✅ 检索数量上限调整为20")
        print("✅ 主标题移至最左侧")
        print("✅ 简洁美观的设计")
        
        print(f"\n🌐 请在浏览器中访问: http://localhost:7862")
        print("📝 测试建议:")
        print("   1. 检查页面是否铺满整个浏览器窗口")
        print("   2. 验证左侧配置区域布局是否正确")
        print("   3. 测试三个标签页的切换功能")
        print("   4. 检查Markdown渲染是否正常")
        print("   5. 验证组件边界是否清晰")
        
    else:
        print("❌ 前端测试失败，请检查应用是否正常启动")
    
    print("-" * 50)
    print("🏁 前端功能测试完成")

if __name__ == "__main__":
    main()
