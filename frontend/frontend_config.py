#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

import os

# API配置
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8080/api/v1")

# Gradio配置
GRADIO_HOST = os.getenv("GRADIO_HOST", "0.0.0.0")
GRADIO_PORT = int(os.getenv("GRADIO_PORT", "7862"))
GRADIO_SHARE = os.getenv("GRADIO_SHARE", "false").lower() == "true"
GRADIO_DEBUG = os.getenv("GRADIO_DEBUG", "false").lower() == "true"

# 模型配置
DEFAULT_MODELS = [
    "qwen3_32b",
    "qwen3_235b",
]

# 默认参数
DEFAULT_TOP_K = 20
DEFAULT_MODEL = "qwen3_32b"

# 界面配置
THEME = "soft"  # gradio主题
TITLE = "🤖 问题库AI"
DESCRIPTION = "支持LLM问答、RAG问答、DATAQA问答三种模式"

# 超时配置
REQUEST_TIMEOUT = 600.0  # 请求超时时间（秒）

# 日志配置
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
